{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Problem: Implement an LSTM Model \n", "\n", "### Problem Statement\n", "You are tasked with implementing a simple **LSTM (Long Short-Term Memory)** model in PyTorch. The model should process sequential data using an LSTM layer followed by a fully connected (FC) layer. Your goal is two-fold: one is to implement a LSTM layer from scratch and another using inbuilt pytorch LSTM layer. Compare the results implementing the forward passes for both the LSTM models.\n", "\n", "### Requirements\n", "1. **Define the LSTM Model using a Custom LSTM layer**:\n", "   - Add a `Custom` LSTM layer to the model. The layer must take care of the hidden and cell states\n", "   - Add a **fully connected (FC) layer** that maps the output of the LSTM to the final predictions.\n", "   - Implement the `forward` method to:\n", "     - Pass the input sequence through the LSTM.\n", "     - Feed the output of the LSTM into the fully connected layer for the final output.\n", "\n", "2. **Define the LSTM Model using an in-built LSTM layer**:\n", "  - Same as `1` with only difference that this time define the LSTM layer using pytorch `nn.Module`\n", "\n", "### Constraints\n", "- The LSTM layer should be implemented with a single hidden layer.\n", "- Use a suitable number of input features, hidden units, and output size for the task.\n", "- Make sure the `forward` method returns the output of the fully connected layer after processing the LSTM output.\n", "\n", "\n", "<details>\n", "  <summary>💡 Hint</summary>\n", "  Add the LSTM layer and FC layer in LSTMModel.__init__.\n", "  <br>\n", "  Implement the forward pass to process sequences using the LSTM and FC layers.\n", "  <br> Review Hidden and cell states computation here: [D2l.ai](https://d2l.ai/chapter_recurrent-modern/lstm.html)\n", "</details>"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["import torch\n", "import torch.nn as nn\n", "import torch.optim as optim\n", "import matplotlib.pyplot as plt"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# Generate synthetic sequential data\n", "torch.manual_seed(42)\n", "sequence_length = 10\n", "num_samples = 100\n", "\n", "# Create a sine wave dataset\n", "X = torch.linspace(0, 4 * 3.14159, steps=num_samples).unsqueeze(1)\n", "y = torch.sin(X)\n", "\n", "# Prepare data for LSTM\n", "def create_in_out_sequences(data, seq_length):\n", "    in_seq = []\n", "    out_seq = []\n", "    for i in range(len(data) - seq_length):\n", "        in_seq.append(data[i:i + seq_length])\n", "        out_seq.append(data[i + seq_length])\n", "    return torch.stack(in_seq), torch.stack(out_seq)\n", "\n", "X_seq, y_seq = create_in_out_sequences(y, sequence_length)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["class CustomLSTMModel(nn.Module):\n", "    def __init__(self, input_dim, hidden_units):\n", "        super().__init__()\n", "        weights_biases_init = lambda : (nn.Parameter(torch.randn(input_dim, hidden_units)),\n", "                                        nn.Parameter(torch.randn(hidden_units, hidden_units)),\n", "                                        nn.Parameter(torch.zeros(hidden_units)))\n", "        self.input_dim = input_dim\n", "        self.hidden_units = hidden_units\n", "        self.<PERSON><PERSON>, self.<PERSON><PERSON>, self.bi = weights_biases_init()\n", "        self.Wxf, self.Whf, self.bf = weights_biases_init()\n", "        self.Wxo, self.Who, self.bo = weights_biases_init()\n", "        self.Wxc, self.Whc, self.bc = weights_biases_init()\n", "        self.fc = nn.Linear(hidden_units, 1)\n", "        # print(self.Wxi.shape, self.Whi.shape, self.bi.shape)\n", "        \n", "    def forward(self, inputs, H_C=None):\n", "        # print(inputs.shape, self.Wxi.shape)\n", "        batch_size, seq_len, _ = inputs.shape\n", "        if not H_C:\n", "            H = torch.randn(batch_size, self.hidden_units)\n", "            C = torch.randn(batch_size, self.hidden_units)\n", "        else:\n", "            H, C = H_C\n", "            \n", "        all_hidden_states = []\n", "        for t in range(seq_len):  \n", "            X_t = inputs[:, t, :]\n", "            # print(X.shape, self.Wxi.shape, self.Whi.shape, self.bi.shape)  \n", "            I_t = torch.sigmoid(torch.matmul(X_t, self.Wxi) + torch.matmul(H, self.Whi) + self.bi)\n", "            F_t = torch.sigmoid(torch.matmul(X_t, self.Wxf) + torch.matmul(H, self.Whf) + self.bf)\n", "            O_t = torch.sigmoid(torch.matmul(X_t, self.Wxo) + torch.matmul(H, self.Who) + self.bo)\n", "            C_tilde = torch.tanh(torch.matmul(X_t, self.Wxc) + torch.matmul(H, self.Whc) + self.bc)\n", "            C = F_t * C + I_t * C_tilde\n", "            H = O_t * torch.tanh(C)\n", "            # print(H.shape)\n", "            all_hidden_states.append(<PERSON><PERSON>(1))\n", "            \n", "        outputs = torch.cat(all_hidden_states, dim=1)\n", "        pred = self.fc(outputs)\n", "        # print(pred.shape)\n", "        return pred, (H, C)\n", "        "]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["# Define the LSTM Model\n", "class LSTMModel(nn.Module):\n", "    def __init__(self):\n", "        super(<PERSON><PERSON><PERSON><PERSON><PERSON>, self).__init__()\n", "        self.lstm = nn.LSTM(input_size=1, hidden_size=50, num_layers=1, batch_first=True)\n", "        self.fc = nn.<PERSON>ar(50, 1)\n", "\n", "    def forward(self, x):\n", "        out, _ = self.lstm(x)\n", "        out = self.fc(out[:, -1, :])  # Use the last output of the LSTM\n", "        return out"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["# Initialize the model, loss function, and optimizer\n", "model_custom = CustomLSTMModel(1, 50)\n", "model_inbuilt = LSTMModel()\n", "criterion = nn.MS<PERSON><PERSON>()\n", "optimizer_custom = optim.Adam(model_custom.parameters(), lr=0.01)\n", "optimizer_inbuilt = optim.Adam(model_inbuilt.parameters(), lr=0.01)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Epoch [50/500], Loss: 0.0996\n", "Epoch [100/500], Loss: 0.0898\n", "Epoch [150/500], Loss: 0.0785\n", "Epoch [200/500], Loss: 0.0667\n", "Epoch [250/500], Loss: 0.0559\n", "Epoch [300/500], Loss: 0.0356\n", "Epoch [350/500], Loss: 0.0268\n", "Epoch [400/500], Loss: 0.0229\n", "Epoch [450/500], Loss: 0.0192\n", "Epoch [500/500], Loss: 0.0142\n"]}], "source": ["# Training loop for the custom model\n", "epochs = 500\n", "for epoch in range(epochs):\n", "    # Forward pass\n", "    state = None\n", "    pred, state = model_custom(X_seq, state)\n", "    loss = criterion(pred[:, -1, :], y_seq) # Use the last output of the LSTM\n", "    # Backward pass and optimization\n", "    optimizer_custom.zero_grad()\n", "    loss.backward()\n", "    optimizer_custom.step()\n", "\n", "    # Log progress every 50 epochs\n", "    if (epoch + 1) % 50 == 0:\n", "        print(f\"Epoch [{epoch + 1}/{epochs}], Loss: {loss.item():.4f}\")"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Epoch [50/500], Loss: 0.0010\n", "Epoch [100/500], Loss: 0.0001\n", "Epoch [150/500], Loss: 0.0000\n", "Epoch [200/500], Loss: 0.0000\n", "Epoch [250/500], Loss: 0.0000\n", "Epoch [300/500], Loss: 0.0000\n", "Epoch [350/500], Loss: 0.0000\n", "Epoch [400/500], Loss: 0.0000\n", "Epoch [450/500], Loss: 0.0000\n", "Epoch [500/500], Loss: 0.0000\n"]}], "source": ["# Training loop for the inbuilt model\n", "epochs = 500\n", "for epoch in range(epochs):\n", "    # Forward pass\n", "    pred = model_inbuilt(X_seq)\n", "    loss = criterion(pred, y_seq)\n", "    # Backward pass and optimization\n", "    optimizer_inbuilt.zero_grad()\n", "    loss.backward()\n", "    optimizer_inbuilt.step()\n", "\n", "    # Log progress every 50 epochs\n", "    if (epoch + 1) % 50 == 0:\n", "        print(f\"Epoch [{epoch + 1}/{epochs}], Loss: {loss.item():.4f}\")"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Predictions with Custom Model for new sequence: [0.8722591996192932, 0.8946256041526794, 0.8925198912620544, 0.9096068739891052, 0.7193865776062012, 0.8029537796974182, 0.7611545920372009, 0.6357396841049194, 0.39185675978660583, 0.15291088819503784, 0.0017629489302635193, -0.1545099914073944, -0.2593921720981598, -0.5998544096946716, -0.6508030891418457, -0.7302036881446838, -0.7933940291404724, -0.7178914546966553, -0.8878818154335022, -0.9227510094642639, -0.6303256154060364, -0.9827148914337158, -1.023526906967163, -0.7632614970207214, -0.8843759894371033, -0.6467899680137634, -0.7290364503860474, -0.3021402657032013, -0.23509612679481506, -0.42650213837623596, 0.030517645180225372, 0.08275053650140762, 0.1288076937198639, 0.743124783039093, 0.7048536539077759, 0.7476622462272644, 0.8374730944633484, 0.6570764183998108, 0.9516618847846985, 0.9500941634178162, 0.9618882536888123, 0.8920931220054626, 0.8603857159614563, 0.7005499005317688, 0.90570467710495, 0.7393853068351746, 0.5955331921577454, 0.40855106711387634, 0.28438618779182434, 0.1268976926803589, -0.18761968612670898, -0.16615036129951477, -0.35904431343078613, -0.5928544402122498, -0.7902691960334778, -0.8339560031890869, -0.7381508946418762, -0.9937191605567932, -0.8944850564002991, -0.8618447184562683, -0.8361732363700867, -0.8878591656684875, -0.8476987481117249, -0.9058246612548828, -0.8216201663017273, -0.8061478734016418, -0.5101088285446167, -0.4294678568840027, -0.3295513391494751, -0.011069238185882568, 0.04775863140821457, 0.18089088797569275, 0.15233871340751648, 0.5635582804679871, 0.442098468542099, 0.7315307259559631, 0.7020516991615295, 0.8273202776908875, 0.9707680344581604, 0.967566192150116, 0.8993355631828308, 0.8505983948707581, 0.9234817624092102, 0.8642513155937195, 0.8910333514213562, 0.683143675327301, 0.5588111281394958, 0.4691178500652313, 0.12453547865152359, -0.0231098011136055]\n", "Predictions with In-Built Model: [1.034206509590149, 1.0235415697097778, 0.9870346784591675, 0.9252163767814636, 0.8392543196678162, 0.7310377359390259, 0.6032606363296509, 0.4594343304634094, 0.3037526607513428, 0.1407918781042099, -0.024866577237844467, -0.18892835080623627, -0.3474650979042053, -0.4968107342720032, -0.6334448456764221, -0.754021406173706, -0.8555606007575989, -0.9356716871261597, -0.9926558136940002, -1.0254398584365845, -1.033415675163269, -1.0163079500198364, -0.9741513133049011, -0.9073867201805115, -0.8170420527458191, -0.7049393653869629, -0.5738575458526611, -0.4275510311126709, -0.27054107189178467, -0.10767856985330582, 0.05637140944600105, 0.2174883335828781, 0.37214457988739014, 0.5172036290168762, 0.6496807932853699, 0.7666422128677368, 0.8652748465538025, 0.9430482983589172, 0.997873842716217, 1.0282002687454224, 1.0330510139465332, 1.0120247602462769, 0.9652963280677795, 0.8936432003974915, 0.7985097765922546, 0.6820997595787048, 0.5474390983581543, 0.3983381986618042, 0.23918719589710236, 0.07460938394069672, -0.09090474247932434, -0.25320976972579956, -0.4085005521774292, -0.5531771779060364, -0.6837753653526306, -0.7970666289329529, -0.89028400182724, -0.9613107442855835, -1.0087145566940308, -1.0316288471221924, -1.0295889377593994, -1.0024389028549194, -0.9503586888313293, -0.8739995360374451, -0.7746828198432922, -0.6545992493629456, -0.5169273018836975, -0.3657670021057129, -0.20583371818065643, -0.041961900889873505, 0.12137367576360703, 0.2803019881248474, 0.43148273229599, 0.5718644261360168, 0.6984841227531433, 0.808440625667572, 0.8990126252174377, 0.9678299427032471, 1.01301109790802, 1.0332329273223877, 1.0277496576309204, 0.9963898062705994, 0.9395633935928345, 0.8583051562309265, 0.7543521523475647, 0.6302329897880554, 0.4893004894256592, 0.3356313705444336, 0.17375512421131134, 0.008280998095870018]\n"]}], "source": ["# Testing on new data\n", "test_steps = 100  # Ensure this is greater than sequence_length\n", "X_test = torch.linspace(0, 5 * 3.14159, steps=test_steps).unsqueeze(1)\n", "y_test = torch.sin(X_test)\n", "\n", "# Create test input sequences\n", "X_test_seq, _ = create_in_out_sequences(y_test, sequence_length)\n", "\n", "with torch.no_grad():\n", "    pred_custom, _ = model_custom(X_test_seq)\n", "    pred_inbuilt = model_inbuilt(X_test_seq)\n", "pred_custom = torch.flatten(pred_custom[:, -1, :])\n", "pred_inbuilt = pred_inbuilt.squeeze()\n", "print(f\"Predictions with Custom Model for new sequence: {pred_custom.tolist()}\")\n", "print(f\"Predictions with In-Built Model: {pred_inbuilt.tolist()}\")\n"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["#Plot the predictions\n", "plt.figure()\n", "# plt.plot(y_test, label=\"Ground Truth\")\n", "plt.plot(pred_custom, label=\"custom model\")\n", "plt.plot(pred_inbuilt, label=\"inbuilt model\")\n", "plt.legend()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.9"}}, "nbformat": 4, "nbformat_minor": 2}