{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## Problem: Save and Load Your PyTorch Model\n", "\n", "### Problem Statement\n", "You are tasked with saving a trained PyTorch model to a file and later loading it for inference or further training. This process allows you to persist the trained model and use it in different environments without retraining.\n", "\n", "### Requirements\n", "1. **Save the Model**:\n", "   - Save the model’s **state dictionary** (weights) to a file named `model.pth` using `torch.save`.\n", "   \n", "2. **Load the Model**:\n", "   - Load the saved state dictionary into a new model instance using `torch.load`.\n", "   - Verify that the loaded model works as expected by performing inference or testing."]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import torch\n", "import torch.nn as nn\n", "import torch.optim as optim"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["# Define a simple model\n", "class SimpleModel(nn.Module):\n", "    def __init__(self):\n", "        super(<PERSON><PERSON><PERSON><PERSON>, self).__init__()\n", "        self.fc = nn.Linear(1, 1)\n", "\n", "    def forward(self, x):\n", "        return self.fc(x)\n", "\n", "# Create and train the model\n", "torch.manual_seed(42)\n", "model = SimpleModel()\n", "criterion = nn.MS<PERSON><PERSON>()\n", "optimizer = optim.SGD(model.parameters(), lr=0.01)\n", "\n", "# Training loop\n", "X = torch.rand(100, 1)\n", "y = 3 * X + 2 + torch.randn(100, 1) * 0.1\n", "epochs = 100\n", "for epoch in range(epochs):\n", "    optimizer.zero_grad()\n", "    predictions = model(X)\n", "    loss = criterion(predictions, y)\n", "    loss.backward()\n", "    optimizer.step()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# TODO: Save the model to a file named \"model.pth\"\n", "...\n", "\n", "# TODO: Load the model back from \"model.pth\"\n", "loaded_model = ..."]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Predictions after loading: tensor([[3.3646],\n", "        [4.2802],\n", "        [5.1959]])\n"]}], "source": ["\n", "# Verify the model works after loading\n", "X_test = torch.tensor([[0.5], [1.0], [1.5]])\n", "with torch.no_grad():\n", "    predictions = loaded_model(X_test)\n", "    print(f\"Predictions after loading: {predictions}\")\n"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.4"}}, "nbformat": 4, "nbformat_minor": 2}