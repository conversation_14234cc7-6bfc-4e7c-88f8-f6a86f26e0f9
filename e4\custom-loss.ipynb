{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Problem: Implement Custom Loss Function (Huber Loss)\n", "\n", "### Problem Statement\n", "You are tasked with implementing the **Huber Loss** as a custom loss function in PyTorch. The Huber loss is a robust loss function used in regression tasks, less sensitive to outliers than Mean Squared Error (MSE). It transitions between L2 loss (squared error) and L1 loss (absolute error) based on a threshold parameter $ \\delta $.\n", "\n", "The Huber loss is mathematically defined as:\n", "$$\n", "L_{\\delta}(y, \\hat{y}) = \n", "\\begin{cases} \n", "\\frac{1}{2}(y - \\hat{y})^2 & \\text{for } |y - \\hat{y}| \\leq \\delta, \\\\\n", "\\delta \\cdot (|y - \\hat{y}| - \\frac{1}{2} \\delta) & \\text{for } |y - \\hat{y}| > \\delta,\n", "\\end{cases}\n", "$$\n", "\n", "where:\n", "- $y$ is the true value,\n", "- $\\hat{y}$ is the predicted value,\n", "- $\\delta$ is a threshold parameter that controls the transition between L1 and L2 loss.\n", "\n", "### Requirements\n", "1. **Custom Loss Function**:\n", "   - Implement a class `<PERSON><PERSON><PERSON><PERSON>` inheriting from `torch.nn.<PERSON><PERSON>`.\n", "   - Define the `forward` method to compute the Huber loss as per the formula.\n", "\n", "2. **Usage in a Regression Model**:\n", "   - Integrate the custom loss function into a regression training pipeline.\n", "   - Use it to compute and optimize the loss during model training.\n", "\n", "### Constraints\n", "- The implementation must handle both scalar and batch inputs for $ y $ (true values) and $ \\hat{y} $ (predicted values).\n", "\n", "\n", "Extra Details: https://en.wikipedia.org/wiki/<PERSON><PERSON>_loss\n", "\n", "<details>\n", "  <summary>💡 Hint</summary>\n", "  Some details: https://www.kaggle.com/code/bigironsphere/loss-function-library-keras-pytorch/notebook\n", "</details>"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import torch\n", "import torch.nn as nn\n", "import torch.optim as optim"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"ename": "RuntimeError", "evalue": "Boolean value of Tensor with more than one value is ambiguous", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31m<PERSON>unt<PERSON>E<PERSON><PERSON>\u001b[39m                              <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[2]\u001b[39m\u001b[32m, line 40\u001b[39m\n\u001b[32m     37\u001b[39m \u001b[38;5;28;01mfor\u001b[39;00m epoch \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mrange\u001b[39m(epochs):\n\u001b[32m     38\u001b[39m     \u001b[38;5;66;03m# Forward pass\u001b[39;00m\n\u001b[32m     39\u001b[39m     predictions = model(X)\n\u001b[32m---> \u001b[39m\u001b[32m40\u001b[39m     loss = \u001b[43mcriterion\u001b[49m\u001b[43m(\u001b[49m\u001b[43mpredictions\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43my\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m     42\u001b[39m     \u001b[38;5;66;03m# Backward pass and optimization\u001b[39;00m\n\u001b[32m     43\u001b[39m     optimizer.zero_grad()\n", "\u001b[36mFile \u001b[39m\u001b[32md:\\miniconda3\\Lib\\site-packages\\torch\\nn\\modules\\module.py:1751\u001b[39m, in \u001b[36mModule._wrapped_call_impl\u001b[39m\u001b[34m(self, *args, **kwargs)\u001b[39m\n\u001b[32m   1749\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m._compiled_call_impl(*args, **kwargs)  \u001b[38;5;66;03m# type: ignore[misc]\u001b[39;00m\n\u001b[32m   1750\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m-> \u001b[39m\u001b[32m1751\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_call_impl\u001b[49m\u001b[43m(\u001b[49m\u001b[43m*\u001b[49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32md:\\miniconda3\\Lib\\site-packages\\torch\\nn\\modules\\module.py:1762\u001b[39m, in \u001b[36mModule._call_impl\u001b[39m\u001b[34m(self, *args, **kwargs)\u001b[39m\n\u001b[32m   1757\u001b[39m \u001b[38;5;66;03m# If we don't have any hooks, we want to skip the rest of the logic in\u001b[39;00m\n\u001b[32m   1758\u001b[39m \u001b[38;5;66;03m# this function, and just call forward.\u001b[39;00m\n\u001b[32m   1759\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m (\u001b[38;5;28mself\u001b[39m._backward_hooks \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28mself\u001b[39m._backward_pre_hooks \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28mself\u001b[39m._forward_hooks \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28mself\u001b[39m._forward_pre_hooks\n\u001b[32m   1760\u001b[39m         \u001b[38;5;129;01mor\u001b[39;00m _global_backward_pre_hooks \u001b[38;5;129;01mor\u001b[39;00m _global_backward_hooks\n\u001b[32m   1761\u001b[39m         \u001b[38;5;129;01mor\u001b[39;00m _global_forward_hooks \u001b[38;5;129;01mor\u001b[39;00m _global_forward_pre_hooks):\n\u001b[32m-> \u001b[39m\u001b[32m1762\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mforward_call\u001b[49m\u001b[43m(\u001b[49m\u001b[43m*\u001b[49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m   1764\u001b[39m result = \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[32m   1765\u001b[39m called_always_called_hooks = \u001b[38;5;28mset\u001b[39m()\n", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[2]\u001b[39m\u001b[32m, line 13\u001b[39m, in \u001b[36mHuberLoss.forward\u001b[39m\u001b[34m(self, y_pred, y_true)\u001b[39m\n\u001b[32m     12\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mforward\u001b[39m(\u001b[38;5;28mself\u001b[39m, y_pred, y_true):\n\u001b[32m---> \u001b[39m\u001b[32m13\u001b[39m     \u001b[38;5;28;01mif\u001b[39;00m torch.abs(y_true - y_pred) <= \u001b[38;5;28mself\u001b[39m.delta:\n\u001b[32m     14\u001b[39m         \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[32m0.5\u001b[39m * (y_true - y_pred) ** \u001b[32m2\u001b[39m\n\u001b[32m     15\u001b[39m     \u001b[38;5;28;01melse\u001b[39;00m:\n", "\u001b[31mRuntimeError\u001b[39m: Boolean value of Tensor with more than one value is ambiguous"]}], "source": ["# Generate synthetic data\n", "torch.manual_seed(42)\n", "X = torch.rand(100, 1) * 10  # 100 data points between 0 and 10\n", "y = 2 * X + 3 + torch.randn(100, 1)  # Linear relationship with noise\n", "\n", "#TODO: Define the nn.Module for the Huber Loss\n", "class HuberLoss(nn.Module):\n", "    def __init__(self, delta=1.0):\n", "        super(<PERSON><PERSON><PERSON><PERSON>, self).__init__()\n", "        self.delta = delta\n", "    \n", "    def forward(self, y_pred, y_true):\n", "        diff = y_true - y_pred\n", "        abs_diff = torch.abs(diff)\n", "        \n", "        # Use torch.where for element-wise conditional operation\n", "        loss = torch.where(\n", "            abs_diff <= self.delta,\n", "            0.5 * diff ** 2,  # L2 loss for small errors\n", "            self.delta * (abs_diff - 0.5 * self.delta)  # L1 loss for large errors\n", "        )\n", "        \n", "        return loss.mean()  # Return mean loss over the batch\n", "\n", "\n", "# Define the Linear Regression Model\n", "class LinearRegressionModel(nn.Module):\n", "    def __init__(self):\n", "        super(LinearRegressionModel, self).__init__()\n", "        self.linear = nn.Linear(1, 1)  # Single input and single output\n", "\n", "    def forward(self, x):\n", "        return self.linear(x)\n", "\n", "# Initialize the model, loss function, and optimizer\n", "model = LinearRegressionModel()\n", "#TODO: Add the loss \n", "criterion = HuberLoss(delta=1.0)\n", "optimizer = optim.SGD(model.parameters(), lr=0.01)\n", "\n", "# Training loop\n", "epochs = 1000\n", "for epoch in range(epochs):\n", "    # Forward pass\n", "    predictions = model(X)\n", "    loss = criterion(predictions, y)\n", "\n", "    # Backward pass and optimization\n", "    optimizer.zero_grad()\n", "    loss.backward()\n", "    optimizer.step()\n", "\n", "    # Log progress every 100 epochs\n", "    if (epoch + 1) % 100 == 0:\n", "        print(f\"Epoch [{epoch + 1}/{epochs}], Loss: {loss.item():.4f}\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Display the learned parameters\n", "[w, b] = model.linear.parameters()\n", "print(f\"Learned weight: {w.item():.4f}, Learned bias: {b.item():.4f}\")\n", "\n", "# Testing on new data\n", "X_test = torch.tensor([[4.0], [7.0]])\n", "with torch.no_grad():\n", "    predictions = model(X_test)\n", "    print(f\"Predictions for {X_test.tolist()}: {predictions.tolist()}\")"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}}, "nbformat": 4, "nbformat_minor": 2}