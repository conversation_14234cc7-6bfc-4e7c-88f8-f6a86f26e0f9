{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Problem: Implement an LSTM Model \n", "\n", "### Problem Statement\n", "You are tasked with implementing a simple **LSTM (Long Short-Term Memory)** model in PyTorch. The model should process sequential data using an LSTM layer followed by a fully connected (FC) layer. Your goal is two-fold: one is to implement a LSTM layer from scratch and another using inbuilt pytorch LSTM layer. Compare the results implementing the forward passes for both the LSTM models.\n", "\n", "### Requirements\n", "1. **Define the LSTM Model using Custom LSTM layer**:\n", "   - Add a `Custom` LSTM layer to the model. The layer must take care of the hidden and cell states\n", "   - Add a **fully connected (FC) layer** that maps the output of the LSTM to the final predictions.\n", "   - Implement the `forward` method to:\n", "     - Pass the input sequence through the LSTM.\n", "     - Feed the output of the LSTM into the fully connected layer for the final output.\n", "\n", "2. **Define the LSTM Model using in-built LSTM layer**:\n", "  - Same as `1` with only difference that this time define the LSTM layer using pytorch `nn.Module`\n", "\n", "### Constraints\n", "- The LSTM layer should be implemented with a single hidden layer.\n", "- Use a suitable number of input features, hidden units, and output size for the task.\n", "- Make sure the `forward` method returns the output of the fully connected layer after processing the LSTM output.\n", "\n", "\n", "<details>\n", "  <summary>💡 Hint</summary>\n", "  Add the LSTM layer and FC layer in LSTMModel.__init__.\n", "  <br>\n", "  Implement the forward pass to process sequences using the LSTM and FC layers.\n", "  <br> Review Hidden and cell states computation here: [D2l.ai](https://d2l.ai/chapter_recurrent-modern/lstm.html)\n", "</details>"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["import torch\n", "import torch.nn as nn\n", "import torch.optim as optim"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["# Generate synthetic sequential data\n", "torch.manual_seed(42)\n", "sequence_length = 10\n", "num_samples = 100\n", "\n", "# Create a sine wave dataset\n", "X = torch.linspace(0, 4 * 3.14159, steps=num_samples).unsqueeze(1)\n", "y = torch.sin(X)\n", "\n", "# Prepare data for LSTM\n", "def create_in_out_sequences(data, seq_length):\n", "    in_seq = []\n", "    out_seq = []\n", "    for i in range(len(data) - seq_length):\n", "        in_seq.append(data[i:i + seq_length])\n", "        out_seq.append(data[i + seq_length])\n", "    return torch.stack(in_seq), torch.stack(out_seq)\n", "\n", "X_seq, y_seq = create_in_out_sequences(y, sequence_length)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["# Define the LSTM Model\n", "# TODO: Add LSTM layer, forward implementation\n", "class LSTMModel(nn.Module):\n", "    def __init__(self):\n", "        ...\n", "\n", "    def forward(self, x):\n", "        ...\n", "# Initialize the model, loss function, and optimizer\n", "model = LSTMModel()\n", "criterion = nn.MS<PERSON><PERSON>()\n", "optimizer = optim.<PERSON>(model.parameters(), lr=0.01)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Epoch [50/500], Loss: 0.0004\n", "Epoch [100/500], Loss: 0.0000\n", "Epoch [150/500], Loss: 0.0000\n", "Epoch [200/500], Loss: 0.0000\n", "Epoch [250/500], Loss: 0.0000\n", "Epoch [300/500], Loss: 0.0000\n", "Epoch [350/500], Loss: 0.0000\n", "Epoch [400/500], Loss: 0.0000\n", "Epoch [450/500], Loss: 0.0000\n", "Epoch [500/500], Loss: 0.0000\n"]}], "source": ["# Training loop\n", "epochs = 500\n", "for epoch in range(epochs):\n", "    # Forward pass\n", "    predictions = model(X_seq)\n", "    loss = criterion(predictions, y_seq)\n", "\n", "    # Backward pass and optimization\n", "    optimizer.zero_grad()\n", "    loss.backward()\n", "    optimizer.step()\n", "\n", "    # Log progress every 50 epochs\n", "    if (epoch + 1) % 50 == 0:\n", "        print(f\"Epoch [{epoch + 1}/{epochs}], Loss: {loss.item():.4f}\")"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Predictions for new sequence: [1.0354082584381104, 1.0123006105422974, 0.9615825414657593, 0.8840561509132385, 0.7813034653663635, 0.6558271050453186, 0.5111342668533325, 0.3516756296157837, 0.18258695304393768, 0.009290406480431557]\n"]}], "source": ["# Testing on new data\n", "test_steps = 20  # Ensure this is greater than sequence_length\n", "X_test = torch.linspace(4 * 3.14159, 5 * 3.14159, steps=test_steps).unsqueeze(1)\n", "y_test = torch.sin(X_test)\n", "\n", "# Create test input sequences\n", "X_test_seq, _ = create_in_out_sequences(y_test, sequence_length)\n", "\n", "with torch.no_grad():\n", "    predictions = model(X_test_seq)\n", "    print(f\"Predictions for new sequence: {predictions.squeeze().tolist()}\")\n"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.9"}}, "nbformat": 4, "nbformat_minor": 2}