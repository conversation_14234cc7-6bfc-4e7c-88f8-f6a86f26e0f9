{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Problem: Train a 3D CNN network for segmenting CT images\n", "### Problem Statement\n", "You are tasked with employing and evaluating a 3D CNN model in Pytorch for semantic segmentation on synthetically generated CT images. \n", "Your goal is to review the input and label data shapes. Next, define a MedCNN model class with a `forward` method that emulates a encode-decoder architecture with appropriate input and output channels based on the input shapes.   \n", "\n", "### Requirements\n", "1. **Implement** a MedCNN model class with Conv3D and ConvTranspose3d for downsampling and upsampling respectively.\n", "2. **Define** <PERSON><PERSON> loss for the problem.\n", "2. **Perform** transfer learning from a ResNet18 - a common strategy for custom architectures.\n", "3. **Train** the model for 5 epochs.\n", "### Constraints\n", "- Use `Pytorch` in-built convolution layers\n", "- Ensure, there is a segmentation head at the end of the network\n", "\n", "\n", "<details>\n", "  <summary>💡 Hint</summary>\n", "  - Strip off the `Avgpooling` and linear layers from ResNet18 using `list(resnet_model.children())[:-2]`\n", "  <br>\n", "  - [Conv3D](https://pytorch.org/docs/stable/generated/torch.nn.Conv3d.html)\n", "  <br>\n", "  - [ConvTranspose3D](https://pytorch.org/docs/stable/generated/torch.nn.ConvTranspose3d.html)\n", "  <br>\n", "  - [Forum discussion on model.children](https://discuss.pytorch.org/t/module-children-vs-module-modules/4551)\n", "</details>"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import torch\n", "import torch.nn as nn\n", "import torch.optim as optim\n", "import torchvision\n", "import torchvision.transforms as transforms"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["CT images (train examples) shape: torch.Size([100, 10, 3, 256, 256])\n", "Segmentation binary masks (labels) shape: torch.Size([100, 10, 1, 256, 256])\n"]}], "source": ["# Generate synthetic CT-scan data (batches, slices, RGB) and associated segmentation masks\n", "torch.manual_seed(42)\n", "batch = 100\n", "num_slices = 10\n", "channels = 3\n", "width = 256\n", "height = 256\n", "\n", "ct_images = torch.randn(size=(batch, num_slices, channels, width, height))\n", "segmentation_masks = (torch.randn(size=(batch, num_slices, 1, width, height))>0).float()\n", "\n", "print(f\"CT images (train examples) shape: {ct_images.shape}\")\n", "print(f\"Segmentation binary masks (labels) shape: {segmentation_masks.shape}\")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["# Define the MedCNN class and its forward method\n", "class MedCNN(nn.Module):\n", "    def __init__(self, backbone, out_channel=1):\n", "        super(Med<PERSON><PERSON><PERSON>, self).__init__()\n", "        self.backbone = backbone\n", "        \n", "        # TODO: Add Downsample convolutional layers\n", "        ...\n", "        \n", "        # TODO: Add Upsample convolutional layers\n", "        ...\n", "        \n", "        #TODO: Final convolution layer from 16 to 1 channel\n", "        ...\n", "        \n", "        self.relu = nn.ReLU()\n", "\n", "    def forward(self, x):\n", "        b, d, c, w, h = x.size() #Input size: [B, D, C, W, H]\n", "        print(f\"Input shape [B, D, C, W, H]: {b, d, c, w, h}\")\n", "        \n", "        #TODO: make changes to the shape of the input such that it is compatible with ResNet\n", "        ...        \n", "        \n", "        #TODO: take output features from the backbone ResNet and make it compatible with Conv3D format\n", "        ...\n", "        \n", "        #TODO: Downsampling\n", "        ...\n", "        \n", "        #TODO: Upsampling\n", "\n", "        #TODO: final segmentation head\n", "        ...\n", "        \n", "        return x"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["#TODO: define Dice loss\n", "def compute_dice_loss(pred, labels, eps=1e-8):\n", "    '''\n", "    <PERSON><PERSON><PERSON>\n", "    pred: [B, D, 1, W, H]\n", "    labels: [B, D, 1, W, H]\n", "    \n", "    Returns\n", "    dice_loss: [B, D, 1, W, H]\n", "    '''\n", "    ..."]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/Library/Caches/pypoetry/virtualenvs/mylogs--5zRa99S-py3.10/lib/python3.10/site-packages/torchvision/models/_utils.py:208: UserWarning: The parameter 'pretrained' is deprecated since 0.13 and may be removed in the future, please use 'weights' instead.\n", "  warnings.warn(\n", "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/mylogs--5zRa99S-py3.10/lib/python3.10/site-packages/torchvision/models/_utils.py:223: UserWarning: Arguments other than a weight enum or `None` for 'weights' are deprecated since 0.13 and may be removed in the future. The current behavior is equivalent to passing `weights=ResNet18_Weights.IMAGENET1K_V1`. You can also use `weights=ResNet18_Weights.DEFAULT` to get the most up-to-date weights.\n", "  warnings.warn(msg)\n"]}], "source": ["# Define resnet as the backbone removing the last two layers\n", "resnet_model = torchvision.models.resnet18(pretrained=True)\n", "resnet_model = nn.Sequential(*list(resnet_model.children())[:-2])\n", "\n", "model = MedCNN(backbone=resnet_model)\n", "\n", "optimizer = optim.<PERSON>(model.parameters(), lr=0.01)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["epochs = 5\n", "for epoch in range(epochs):\n", "    optimizer.zero_grad()\n", "    pred = model(ct_images)\n", "    loss = compute_dice_loss(pred, segmentation_masks)\n", "    loss.backward()\n", "    optimizer.step()\n", "    print(f\"Loss at epoch {epoch}: {loss}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "mylogs--5zRa99S-py3.10", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.9"}}, "nbformat": 4, "nbformat_minor": 2}