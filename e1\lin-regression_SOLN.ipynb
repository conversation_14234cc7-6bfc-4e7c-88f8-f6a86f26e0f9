{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Problem: Implement Linear Regression\n", "\n", "### Problem Statement\n", "Your task is to implement a **Linear Regression** model using PyTorch. The model should predict a continuous target variable based on a given set of input features.\n", "\n", "### Requirements\n", "1. **Model Definition**:\n", "   - Implement a class `LinearRegressionModel` with:\n", "     - A single linear layer mapping input features to the target variable.\n", "2. **Forward Method**:\n", "   - Implement the `forward` method to compute predictions given input data."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import torch\n", "import torch.nn as nn\n", "import torch.optim as optim"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Epoch [100/1000], Loss: 1.6039\n", "Epoch [200/1000], Loss: 1.0242\n", "Epoch [300/1000], Loss: 0.8017\n", "Epoch [400/1000], Loss: 0.7163\n", "Epoch [500/1000], Loss: 0.6836\n", "Epoch [600/1000], Loss: 0.6710\n", "Epoch [700/1000], Loss: 0.6662\n", "Epoch [800/1000], Loss: 0.6643\n", "Epoch [900/1000], Loss: 0.6636\n", "Epoch [1000/1000], Loss: 0.6634\n"]}], "source": ["# Generate synthetic data\n", "torch.manual_seed(42)\n", "X = torch.rand(100, 1) * 10  # 100 data points between 0 and 10\n", "y = 2 * X + 3 + torch.randn(100, 1)  # Linear relationship with noise\n", "\n", "# Define the Linear Regression Model\n", "class LinearRegressionModel(nn.Module):\n", "    def __init__(self):\n", "        super(LinearRegressionModel, self).__init__()\n", "        self.linear = nn.Linear(1, 1)  # Single input and single output\n", "\n", "    def forward(self, x):\n", "        return self.linear(x)\n", "\n", "# Initialize the model, loss function, and optimizer\n", "model = LinearRegressionModel()\n", "criterion = nn.MS<PERSON><PERSON>()\n", "optimizer = optim.SGD(model.parameters(), lr=0.01)\n", "\n", "# Training loop\n", "epochs = 1000\n", "for epoch in range(epochs):\n", "    # Forward pass\n", "    predictions = model(X)\n", "    loss = criterion(predictions, y)\n", "\n", "    # Backward pass and optimization\n", "    optimizer.zero_grad()\n", "    loss.backward()\n", "    optimizer.step()\n", "\n", "    # Log progress every 100 epochs\n", "    if (epoch + 1) % 100 == 0:\n", "        print(f\"Epoch [{epoch + 1}/{epochs}], Loss: {loss.item():.4f}\")\n"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Learned weight: 1.9577, Learned bias: 3.2045\n", "Predictions for [[4.0], [7.0]]: [[11.035286903381348], [16.90837860107422]]\n"]}], "source": ["# Display the learned parameters\n", "[w, b] = model.linear.parameters()\n", "print(f\"Learned weight: {w.item():.4f}, Learned bias: {b.item():.4f}\")\n", "\n", "# Testing on new data\n", "X_test = torch.tensor([[4.0], [7.0]])\n", "with torch.no_grad():\n", "    predictions = model(X_test)\n", "    print(f\"Predictions for {X_test.tolist()}: {predictions.tolist()}\")"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}}, "nbformat": 4, "nbformat_minor": 2}