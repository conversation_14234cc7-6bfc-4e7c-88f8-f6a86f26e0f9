{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Problem: Write a Custom Activation Function\n", "\n", "### Problem Statement\n", "You are tasked with implementing a **custom activation function** in PyTorch that computes the following operation: \n", " \n", "$ \\text{activation}(x) = \\tanh(x) + x $ \n", "\n", "Once implemented, this custom activation function will be used in a simple linear regression model.\n", "\n", "### Requirements\n", "1. **Custom Activation Function**:\n", "   - Implement a class `CustomActivationModel` inheriting from `torch.nn.Module`.\n", "   - Define the `forward` method to compute the activation function \\( \\text{tanh}(x) + x \\).\n", "\n", "2. **Integration with Linear Regression**:\n", "   - Use the custom activation function in a simple linear regression model.\n", "   - The model should include:\n", "     - A single linear layer.\n", "     - The custom activation function applied to the output of the linear layer.\n", "\n", "### Constraints\n", "- The custom activation function should not have any learnable parameters.\n", "- Ensure compatibility with PyTorch tensors for forward pass computations.\n", "\n", "<details>\n", "  <summary>💡 Hint</summary>\n", "  Some details: https://stackoverflow.com/questions/55765234/pytorch-custom-activation-functions\n", "</details>"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import torch\n", "import torch.nn as nn\n", "import torch.optim as optim"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# Generate synthetic data\n", "torch.manual_seed(42)\n", "X = torch.rand(100, 1) * 10  # 100 data points between 0 and 10\n", "y = 2 * X + 3 + torch.randn(100, 1)  # Linear relationship with noise"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Epoch [100/1000], Loss: 1.0552\n", "Epoch [200/1000], Loss: 0.8031\n", "Epoch [300/1000], Loss: 0.7150\n", "Epoch [400/1000], Loss: 0.6826\n", "Epoch [500/1000], Loss: 0.6705\n", "Epoch [600/1000], Loss: 0.6659\n", "Epoch [700/1000], Loss: 0.6642\n", "Epoch [800/1000], Loss: 0.6635\n", "Epoch [900/1000], Loss: 0.6632\n", "Epoch [1000/1000], Loss: 0.6632\n"]}], "source": ["# Define the Linear Regression Model\n", "class CustomActivationModel(nn.Module):\n", "    def __init__(self):\n", "        super(CustomActivationModel, self).__init__()\n", "        self.linear = nn.Linear(1, 1)  # Single input and single output\n", "\n", "    # TODO: Implement the forward pass\n", "    def custom_activation(self, x):\n", "        return torch.tanh(x) + x\n", "        ...\n", "            \n", "    # TODO: Implement the forward pass\n", "    def forward(self, x):\n", "        return self.custom_activation(self.linear(x))\n", "        ...\n", "\n", "# Initialize the model, loss function, and optimizer\n", "model = CustomActivationModel()\n", "criterion = nn.MS<PERSON><PERSON>()\n", "optimizer = optim.SGD(model.parameters(), lr=0.01)\n", "\n", "# Training loop\n", "epochs = 1000\n", "for epoch in range(epochs):\n", "    # Forward pass\n", "    predictions = model(X)\n", "    loss = criterion(predictions, y)\n", "\n", "    # Backward pass and optimization\n", "    optimizer.zero_grad()\n", "    loss.backward()\n", "    optimizer.step()\n", "\n", "    # Log progress every 100 epochs\n", "    if (epoch + 1) % 100 == 0:\n", "        print(f\"Epoch [{epoch + 1}/{epochs}], Loss: {loss.item():.4f}\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Learned weight: 1.9557, Learned bias: 2.2181\n", "Predictions for [[4.0], [7.0]]: [[11.04088020324707], [16.907970428466797]]\n"]}], "source": ["# Display the learned parameters\n", "[w, b] = model.linear.parameters()\n", "print(f\"Learned weight: {w.item():.4f}, Learned bias: {b.item():.4f}\")\n", "\n", "# Testing on new data\n", "X_test = torch.tensor([[4.0], [7.0]])\n", "with torch.no_grad():\n", "    predictions = model(X_test)\n", "    print(f\"Predictions for {X_test.tolist()}: {predictions.tolist()}\")"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}}, "nbformat": 4, "nbformat_minor": 2}