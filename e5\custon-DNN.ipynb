{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Problem: Implement a Deep Neural Network\n", "\n", "### Problem Statement\n", "You are tasked with constructing a **Deep Neural Network (DNN)** model to solve a regression task using PyTorch. The objective is to predict target values from synthetic data exhibiting a non-linear relationship.\n", "\n", "### Requirements\n", "Implement the `DNNModel` class that satisfies the following criteria:\n", "\n", "1. **Model Definition**:\n", "   - The model should have:\n", "     - An **input layer** connected to a **hidden layer**.\n", "     - A **ReLU activation function** for non-linearity.\n", "     - An **output layer** with a single unit for regression.\n", "\n", "<details> <summary>💡 Hint</summary> - Use `nn.Sequential` to simplify the implementation of the `DNNModel`. - Experiment with different numbers of layers and hidden units to optimize performance. - Ensure the final layer has a single output unit (since it's a regression task). </details> <details> <summary>💡 Bonus: Try Custom Loss Functions</summary> Experiment with custom loss functions (e.g., Huber Loss) and compare their performance with MSE. </details>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import torch\n", "import torch.nn as nn\n", "import torch.optim as optim"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Generate synthetic data\n", "torch.manual_seed(42)\n", "X = torch.rand(100, 2) * 10  # 100 data points with 2 features\n", "y = (X[:, 0] + X[:, 1] * 2).unsqueeze(1) + torch.randn(100, 1)  # Non-linear relationship with noise"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Define the Deep Neural Network Model\n", "class DNNModel(nn.Module):\n", "    def __init__(self):\n", "        ...\n", "\n", "    def forward(self, x):\n", "        ...\n", "        return x"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Epoch [100/1000], Loss: 2.3831\n", "Epoch [200/1000], Loss: 0.8492\n", "Epoch [300/1000], Loss: 0.7732\n", "Epoch [400/1000], Loss: 0.7514\n", "Epoch [500/1000], Loss: 0.7371\n", "Epoch [600/1000], Loss: 0.7291\n", "Epoch [700/1000], Loss: 0.7251\n", "Epoch [800/1000], Loss: 0.7233\n", "Epoch [900/1000], Loss: 0.7225\n", "Epoch [1000/1000], Loss: 0.7221\n", "Predictions for [[4.0, 3.0], [7.0, 8.0]]: [[9.915834426879883], [23.081729888916016]]\n"]}], "source": ["# Initialize the model, loss function, and optimizer\n", "model = DNNModel()\n", "criterion = nn.MS<PERSON><PERSON>()\n", "optimizer = optim.<PERSON>(model.parameters(), lr=0.01)\n", "\n", "# Training loop\n", "epochs = 1000\n", "for epoch in range(epochs):\n", "    # Forward pass\n", "    predictions = model(X)\n", "    loss = criterion(predictions, y)\n", "\n", "    # Backward pass and optimization\n", "    optimizer.zero_grad()\n", "    loss.backward()\n", "    optimizer.step()\n", "\n", "    # Log progress every 100 epochs\n", "    if (epoch + 1) % 100 == 0:\n", "        print(f\"Epoch [{epoch + 1}/{epochs}], Loss: {loss.item():.4f}\")\n", "\n", "# Testing on new data\n", "X_test = torch.tensor([[4.0, 3.0], [7.0, 8.0]])\n", "with torch.no_grad():\n", "    predictions = model(X_test)\n", "    print(f\"Predictions for {X_test.tolist()}: {predictions.tolist()}\")"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.4"}}, "nbformat": 4, "nbformat_minor": 2}