{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Problem: Implement a Deep Neural Network\n", "\n", "### Problem Statement\n", "You are tasked with constructing a **Deep Neural Network (DNN)** model to solve a regression task using PyTorch. The objective is to predict target values from synthetic data exhibiting a non-linear relationship.\n", "\n", "### Requirements\n", "Implement the `DNNModel` class that satisfies the following criteria:\n", "\n", "1. **Model Definition**:\n", "   - The model should have:\n", "     - An **input layer** connected to a **hidden layer**.\n", "     - A **ReLU activation function** for non-linearity.\n", "     - An **output layer** with a single unit for regression.\n", "\n", "<details> <summary>💡 Hint</summary> - Use `nn.Sequential` to simplify the implementation of the `DNNModel`. - Experiment with different numbers of layers and hidden units to optimize performance. - Ensure the final layer has a single output unit (since it's a regression task). </details> <details> <summary>💡 Bonus: Try Custom Loss Functions</summary> Experiment with custom loss functions (e.g., Huber Loss) and compare their performance with MSE. </details>"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["import torch\n", "import torch.nn as nn\n", "import torch.optim as optim"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["# Generate synthetic data\n", "torch.manual_seed(42)\n", "X = torch.rand(100, 2) * 10  # 100 data points with 2 features\n", "y = (X[:, 0] + X[:, 1] * 2).unsqueeze(1) + torch.randn(100, 1)  # Non-linear relationship with noise"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["# Define the Deep Neural Network Model\n", "class DNNModel(nn.Module):\n", "    def __init__(self):\n", "        super(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, self).__init__()\n", "        self.layer1 = nn.Sequential(\n", "            nn.<PERSON><PERSON>(2, 10),\n", "            nn.ReLU(),\n", "            nn.<PERSON><PERSON>(10, 1)\n", "        )\n", "\n", "    def forward(self, x):\n", "        return self.layer1(x)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Epoch [100/1000], Loss: 2.3831\n", "Epoch [200/1000], Loss: 0.8492\n", "Epoch [300/1000], Loss: 0.7732\n", "Epoch [400/1000], Loss: 0.7514\n", "Epoch [500/1000], Loss: 0.7371\n", "Epoch [600/1000], Loss: 0.7291\n", "Epoch [700/1000], Loss: 0.7251\n", "Epoch [800/1000], Loss: 0.7233\n", "Epoch [900/1000], Loss: 0.7225\n", "Epoch [1000/1000], Loss: 0.7221\n", "Predictions for [[4.0, 3.0], [7.0, 8.0]]: [[9.915834426879883], [23.08173179626465]]\n", "Expected true values: [10.0, 23.0]\n", "Prediction errors: [0.08416557312011719, 0.08173179626464844]\n", "Mean absolute error: 0.0829\n"]}], "source": ["# Initialize the model, loss function, and optimizer\n", "model = DNNModel()\n", "criterion = nn.MS<PERSON><PERSON>()\n", "optimizer = optim.<PERSON>(model.parameters(), lr=0.01)\n", "\n", "# Training loop\n", "epochs = 1000\n", "for epoch in range(epochs):\n", "    # Forward pass\n", "    predictions = model(X)\n", "    loss = criterion(predictions, y)\n", "\n", "    # Backward pass and optimization\n", "    optimizer.zero_grad()\n", "    loss.backward()\n", "    optimizer.step()\n", "\n", "    # Log progress every 100 epochs\n", "    if (epoch + 1) % 100 == 0:\n", "        print(f\"Epoch [{epoch + 1}/{epochs}], Loss: {loss.item():.4f}\")\n", "\n", "# Testing on new data\n", "X_test = torch.tensor([[4.0, 3.0], [7.0, 8.0]])\n", "with torch.no_grad():\n", "    predictions = model(X_test)\n", "    print(f\"Predictions for {X_test.tolist()}: {predictions.tolist()}\")\n", "    \n", "    # Calculate expected true values (without noise)\n", "    true_values = X_test[:, 0] + X_test[:, 1] * 2\n", "    print(f\"Expected true values: {true_values.tolist()}\")\n", "    \n", "    # Calculate prediction errors\n", "    errors = torch.abs(predictions.squeeze() - true_values)\n", "    print(f\"Prediction errors: {errors.tolist()}\")\n", "    print(f\"Mean absolute error: {errors.mean().item():.4f}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== Training Data Analysis ===\n", "Training MSE Loss: 0.7221\n", "Training MAE: 0.6798\n", "Training data noise level (std): 0.8667\n", "\n", "First 5 training examples:\n", "Input: [8.82269287109375, 9.150039672851562], True(no noise): 27.12, True(with noise): 26.96, Pred: 27.30\n", "Input: [3.8286375999450684, 9.593056678771973], True(no noise): 23.01, True(with noise): 22.59, Pred: 22.95\n", "Input: [3.904482126235962, 6.00895357131958], True(no noise): 15.92, True(with noise): 16.87, Pred: 15.85\n", "Input: [2.5657248497009277, 7.936413288116455], True(no noise): 18.44, True(with noise): 18.25, Pred: 18.31\n", "Input: [9.40771484375, 1.3318592309951782], True(no noise): 12.07, True(with noise): 13.13, Pred: 12.24\n"]}, {"ename": "", "evalue": "", "output_type": "error", "traceback": ["\u001b[1;31m在当前单元格或上一个单元格中执行代码时 Kernel 崩溃。\n", "\u001b[1;31m请查看单元格中的代码，以确定故障的可能原因。\n", "\u001b[1;31m单击<a href='https://aka.ms/vscodeJupyterKernelCrash'>此处</a>了解详细信息。\n", "\u001b[1;31m有关更多详细信息，请查看 Jupyter <a href='command:jupyter.viewOutput'>log</a>。"]}], "source": ["# Analyze training data performance\n", "print(\"=== Training Data Analysis ===\")\n", "with torch.no_grad():\n", "    train_predictions = model(X)\n", "    train_true_values = X[:, 0] + X[:, 1] * 2  # Without noise\n", "    train_errors = torch.abs(train_predictions.squeeze() - y.squeeze())\n", "    \n", "    print(f\"Training MSE Loss: {criterion(train_predictions, y).item():.4f}\")\n", "    print(f\"Training MAE: {train_errors.mean().item():.4f}\")\n", "    print(f\"Training data noise level (std): {(y.squeeze() - train_true_values).std().item():.4f}\")\n", "    \n", "    # Show some training examples\n", "    print(\"\\nFirst 5 training examples:\")\n", "    for i in range(5):\n", "        true_no_noise = train_true_values[i].item()\n", "        true_with_noise = y[i].item()\n", "        pred = train_predictions[i].item()\n", "        print(f\"Input: {X[i].tolist()}, True(no noise): {true_no_noise:.2f}, True(with noise): {true_with_noise:.2f}, Pred: {pred:.2f}\")"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}}, "nbformat": 4, "nbformat_minor": 2}