{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Problem: Implement parameter initialization strategies for a CNN model in Pytorch\n", "\n", "### Problem Statement\n", "You are tasked with employing and evaluating a CNN model\\'s parameter initialization strategies in Pytorch. \n", "Your goal is to initialize the weights and biases of a vanilla CNN model provided in the problem statement and comment on the implications of each strategy.\n", "\n", "### Requirements\n", "1. **Initialize** weights and biases in the following ways:\n", "   - **Zero Initialization**: set the parameters to zero\n", "   - **Random Initialization**: sets model parameters to random values drawn from a normal distribution \n", "   - **Xavier Initialization** sets them to random values from a normal distribution with **mean=0 and variance=1\\/n**\n", "   - **Kai<PERSON> He Initialization** initializes to random values from a normal distribution with **mean=0 and variance=2\\/n**\n", "2. Train and compute accuracy for each strategy\n", "### Constraints\n", "- Use the given CNN model and the training and testing helper functions for accuracy computations.\n", "- Ensure the model is compatible with the CIFAR-10 dataset, which contains 10 classes.\n", "\n", "\n", "<details>\n", "  <summary>💡 Hint</summary>\n", "  - Use `torch.nn.init` for weight initialization\n", "  <br>\n", "  - Resources to read: [All you need is a good init](https://arxiv.org/pdf/1511.06422)\n", "</details>"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["import torch\n", "import torch.nn as nn\n", "import torch.optim as optim\n", "import torchvision\n", "import torchvision.transforms as transforms"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Files already downloaded and verified\n", "Files already downloaded and verified\n"]}], "source": ["transform = transforms.Compose([\n", "    transforms.To<PERSON><PERSON><PERSON>(),\n", "    transforms.Normalize((0.5, 0.5, 0.5), (0.5, 0.5, 0.5))\n", "])\n", "\n", "train_dataset = torchvision.datasets.CIFAR10(root='./data', train=True, download=True, transform=transform)\n", "train_loader = torch.utils.data.DataLoader(train_dataset, batch_size=32, shuffle=True)\n", "\n", "test_dataset = torchvision.datasets.CIFAR10(root='./data', train=False, download=True, transform=transform)\n", "test_loader = torch.utils.data.DataLoader(test_dataset, batch_size=32, shuffle=True)\n"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["def train_test_loop(model, train_loader, test_loader, epochs=10):\n", "    model.train()\n", "    criterion = nn.CrossEntropyLoss()\n", "    optimizer = optim.<PERSON>(model.parameters(), lr=0.001)\n", "    \n", "    for epoch in range(epochs):\n", "        for image, label in train_loader:\n", "            pred = model(image)\n", "            loss = criterion(pred, label)\n", "            \n", "            optimizer.zero_grad()\n", "            loss.backward()\n", "            optimizer.step()    \n", "        print(f\"Training loss at epoch {epoch} = {loss.item()}\")\n", "    \n", "    model.eval()\n", "    correct = 0\n", "    total = 0\n", "    with torch.no_grad():\n", "        for image_test, label_test in test_loader:\n", "            pred_test = model(image_test)\n", "            _, pred_test_vals = torch.max(pred_test, dim=1)\n", "            total += label_test.size(0)\n", "            correct += (pred_test_vals == label_test).sum().item()\n", "    print(f\"Test Accuracy = {(correct * 100)/total}\")"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["class VanillaCNNModel(nn.Module):\n", "    def __init__(self):\n", "        super().__init__()\n", "        self.conv1 = nn.Conv2d(3, 32, kernel_size=3, stride=1, padding=1)\n", "        self.conv2 = nn.Conv2d(32, 64, kernel_size=3, stride=1, padding=1)\n", "        self.pool = nn.MaxPool2d(kernel_size=2, stride=2)\n", "        self.fc1 = nn.Linear(64*16*16, 128)\n", "        self.fc2 = nn.<PERSON>ar(128, 10)\n", "        self.relu = nn.ReLU()\n", "    \n", "    def forward(self, x):\n", "        x = self.relu(self.conv1(x))\n", "        x = self.pool(self.relu(self.conv2(x)))\n", "        x = x.view(x.size(0), -1)\n", "        x = self.relu(self.fc1(x))\n", "        x = self.fc2(x)\n", "        return x"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["def config_init(init_type=\"kaiming\"):\n", "    \n", "    def kaiming_init(m):\n", "        if isinstance(m, nn.Conv2d):\n", "            nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')\n", "            if m.bias is not None:\n", "                nn.init.zeros_(m.bias)\n", "        elif isinstance(m, nn.Linear):\n", "            nn.init.kaiming_normal_(m.weight)\n", "            nn.init.constant_(m.bias, 0)\n", "            \n", "            \n", "    def xavier_init(m):\n", "        if isinstance(m, (nn.Conv2d, nn.Linear)):\n", "            nn.init.xavier_normal_(m.weight)\n", "            if m.bias is not None:\n", "                nn.init.zeros_(m.bias)\n", "                \n", "    def zeros_init(m):\n", "        if isinstance(m, (nn.Conv2d, nn.Linear)):\n", "            nn.init.zeros_(m.weight)\n", "            nn.init.zeros_(m.bias)\n", "            \n", "    def random_init(m):\n", "        if isinstance(m, (nn.Conv2d, nn.Linear)):\n", "            nn.init.normal_(m.weight)\n", "            nn.init.normal_(m.bias)\n", "    \n", "\n", "    initializer_dict = {\"kaiming\": kaiming_init,\n", "                        \"xavier\": xavier_init,\n", "                        \"zeros\": zeros_init,\n", "                        \"random\": random_init}\n", "    \n", "    return initializer_dict.get(init_type)"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["_________Vanilla_______________________\n", "Training loss at epoch 0 = 1.2211995124816895\n", "Training loss at epoch 1 = 1.006090521812439\n", "Training loss at epoch 2 = 0.7061297297477722\n", "Training loss at epoch 3 = 0.7647947072982788\n", "Training loss at epoch 4 = 0.10774677246809006\n", "Training loss at epoch 5 = 0.27187561988830566\n", "Training loss at epoch 6 = 0.20480391383171082\n", "Training loss at epoch 7 = 0.1830582171678543\n", "Training loss at epoch 8 = 0.0019390585366636515\n", "Training loss at epoch 9 = 0.21471130847930908\n", "Test Accuracy = 66.88\n", "_________Kaiming_______________________\n", "Training loss at epoch 0 = 0.7257033586502075\n", "Training loss at epoch 1 = 0.702377438545227\n", "Training loss at epoch 2 = 0.5876032710075378\n", "Training loss at epoch 3 = 0.6681772470474243\n", "Training loss at epoch 4 = 0.18335723876953125\n", "Training loss at epoch 5 = 0.4135642349720001\n", "Training loss at epoch 6 = 0.035230498760938644\n", "Training loss at epoch 7 = 0.1903143674135208\n", "Training loss at epoch 8 = 0.10529276728630066\n", "Training loss at epoch 9 = 0.19404983520507812\n", "Test Accuracy = 67.78\n", "_________Xavier_______________________\n", "Training loss at epoch 0 = 1.1875104904174805\n", "Training loss at epoch 1 = 0.7560771703720093\n", "Training loss at epoch 2 = 0.6299848556518555\n", "Training loss at epoch 3 = 0.2939636707305908\n", "Training loss at epoch 4 = 0.5459955930709839\n", "Training loss at epoch 5 = 0.10567782819271088\n", "Training loss at epoch 6 = 0.04481416195631027\n", "Training loss at epoch 7 = 0.03469347208738327\n", "Training loss at epoch 8 = 0.06539886444807053\n", "Training loss at epoch 9 = 0.13839924335479736\n", "Test Accuracy = 67.16\n", "_________Zeros_______________________\n", "Training loss at epoch 0 = 2.3013572692871094\n", "Training loss at epoch 1 = 2.3004114627838135\n", "Training loss at epoch 2 = 2.3048877716064453\n", "Training loss at epoch 3 = 2.3059403896331787\n", "Training loss at epoch 4 = 2.3007495403289795\n", "Training loss at epoch 5 = 2.3020472526550293\n", "Training loss at epoch 6 = 2.3068177700042725\n", "Training loss at epoch 7 = 2.3013417720794678\n", "Training loss at epoch 8 = 2.3053534030914307\n", "Training loss at epoch 9 = 2.3037328720092773\n", "Test Accuracy = 10.0\n", "_________Random_______________________\n", "Training loss at epoch 0 = 2.3724217414855957\n", "Training loss at epoch 1 = 2.2727415561676025\n", "Training loss at epoch 2 = 2.3046958446502686\n", "Training loss at epoch 3 = 2.2996785640716553\n", "Training loss at epoch 4 = 2.3016655445098877\n", "Training loss at epoch 5 = 2.30169415473938\n", "Training loss at epoch 6 = 2.303645133972168\n", "Training loss at epoch 7 = 2.3072891235351562\n", "Training loss at epoch 8 = 2.305885076522827\n", "Training loss at epoch 9 = 2.303130626678467\n", "Test Accuracy = 10.0\n"]}], "source": ["\n", "for name, model in zip([\"<PERSON><PERSON>\", \"<PERSON><PERSON>\", \"<PERSON>\", \"<PERSON><PERSON>\", \"Random\"], [VanillaCNNModel(),\n", "              VanillaCNNModel().apply(config_init(\"kaiming\")),\n", "              VanillaCNNModel().apply(config_init(\"xavier\")),\n", "              VanillaCNNModel().apply(config_init(\"zeros\")),\n", "              VanillaCNNModel().apply(config_init(\"random\"))\n", "              ]):\n", "    print(f\"_________{name}_______________________\")\n", "    train_test_loop(model, train_loader, test_loader)\n", "    "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "mylogs--5zRa99S-py3.10", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.9"}}, "nbformat": 4, "nbformat_minor": 2}